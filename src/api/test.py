"""
测试API接口

提供算法测试的HTTP接口，支持单个算法测试和综合测试。
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

from fastapi import APIRouter, HTTPException, Depends, Request, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from ..services.test_service import TestService
from ..utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)

router = APIRouter()


# 请求模型
class TestRequest(BaseModel):
    """测试请求模型"""
    parameters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="自定义测试参数，如果不提供则使用默认参数"
    )
    case_names: Optional[List[str]] = Field(
        default=None,
        description="要运行的测试用例名称列表，如果不提供则运行所有用例"
    )


# 响应模型
class TestCaseResult(BaseModel):
    """单个测试用例结果"""
    case_name: str
    status: str  # passed, failed, error
    execution_time: float
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TestSummary(BaseModel):
    """测试摘要"""
    total_cases: int
    passed: int
    failed: int
    error: int
    success_rate: float
    avg_execution_time: float


class TestResponse(BaseModel):
    """测试响应模型"""
    algorithm: str
    status: str  # completed, error
    test_cases: List[TestCaseResult] = []
    summary: TestSummary
    timestamp: str
    debug_info: Optional[Dict[str, Any]] = None


class AllTestsResponse(BaseModel):
    """所有算法测试响应模型"""
    status: str
    algorithms: Dict[str, Any]
    overall_summary: Dict[str, Any]
    timestamp: str


def get_test_service(request: Request) -> TestService:
    """
    获取测试服务实例
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        TestService: 测试服务实例
    """
    if not hasattr(request.app.state, 'test_service'):
        # 如果没有测试服务实例，创建一个
        config_manager = request.app.state.config
        request.app.state.test_service = TestService(config_manager)
    
    return request.app.state.test_service


@router.get("/test/algorithms", response_model=List[str])
async def list_algorithms(
    test_service: TestService = Depends(get_test_service)
) -> List[str]:
    """
    获取支持的算法列表
    
    Returns:
        List[str]: 算法名称列表
    """
    try:
        algorithms = await test_service.list_available_algorithms()
        return algorithms
    except Exception as e:
        logger.error(f"获取算法列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取算法列表失败: {str(e)}")


@router.get("/test/cases/{algorithm}", response_model=List[str])
async def list_test_cases(
    algorithm: str,
    test_service: TestService = Depends(get_test_service)
) -> List[str]:
    """
    获取指定算法的测试用例列表
    
    Args:
        algorithm: 算法名称
        
    Returns:
        List[str]: 测试用例名称列表
    """
    try:
        cases = await test_service.list_test_cases(algorithm)
        return cases
    except Exception as e:
        logger.error(f"获取算法 {algorithm} 的测试用例失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"获取算法 {algorithm} 的测试用例失败: {str(e)}"
        )


@router.get("/test/{algorithm}")
async def test_algorithm_get(
    algorithm: str,
    case_names: Optional[str] = Query(
        default=None, 
        description="测试用例名称，多个用逗号分隔"
    ),
    test_service: TestService = Depends(get_test_service)
) -> Dict[str, Any]:
    """
    使用默认参数测试指定算法 (GET方法)
    
    Args:
        algorithm: 算法名称 (water_level, flow_speed, anomaly)
        case_names: 测试用例名称，多个用逗号分隔
        
    Returns:
        Dict[str, Any]: 测试结果
    """
    try:
        # 解析测试用例名称
        case_list = None
        if case_names:
            case_list = [name.strip() for name in case_names.split(",")]
        
        logger.info(f"开始测试算法: {algorithm}, 用例: {case_list}")
        
        # 执行测试
        result = await test_service.test_algorithm(
            algorithm=algorithm,
            case_names=case_list
        )
        
        return result
        
    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"测试算法 {algorithm} 失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"测试算法 {algorithm} 失败: {str(e)}"
        )


@router.post("/test/{algorithm}")
async def test_algorithm_post(
    algorithm: str,
    request: TestRequest,
    test_service: TestService = Depends(get_test_service)
) -> Dict[str, Any]:
    """
    使用自定义参数测试指定算法 (POST方法)
    
    Args:
        algorithm: 算法名称 (water_level, flow_speed, anomaly)
        request: 测试请求，包含自定义参数和用例名称
        
    Returns:
        Dict[str, Any]: 测试结果
    """
    try:
        logger.info(f"开始测试算法: {algorithm}, 自定义参数: {request.parameters}")
        
        # 执行测试
        result = await test_service.test_algorithm(
            algorithm=algorithm,
            case_names=request.case_names,
            custom_parameters=request.parameters
        )
        
        return result
        
    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"测试算法 {algorithm} 失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"测试算法 {algorithm} 失败: {str(e)}"
        )


@router.get("/test/all")
async def test_all_algorithms(
    test_service: TestService = Depends(get_test_service)
) -> Dict[str, Any]:
    """
    测试所有算法
    
    Returns:
        Dict[str, Any]: 所有算法的测试结果
    """
    try:
        logger.info("开始测试所有算法")
        
        result = await test_service.test_all_algorithms()
        
        logger.info("所有算法测试完成")
        return result
        
    except Exception as e:
        logger.error(f"测试所有算法失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"测试所有算法失败: {str(e)}"
        )


@router.get("/test/{algorithm}/parameters")
async def get_default_parameters(
    algorithm: str,
    test_service: TestService = Depends(get_test_service)
) -> Dict[str, Any]:
    """
    获取算法的默认测试参数
    
    Args:
        algorithm: 算法名称
        
    Returns:
        Dict[str, Any]: 默认测试参数
    """
    try:
        parameters = await test_service.get_default_test_parameters(algorithm)
        return {
            "algorithm": algorithm,
            "default_parameters": parameters,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取算法 {algorithm} 的默认参数失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"获取算法 {algorithm} 的默认参数失败: {str(e)}"
        )
