"""
E字母形状识别算法

从旧项目移植的E字母识别功能，用于检测水尺上的E字母标记。
基于 old_src/water_level/src/utils/shape_recognition_utils.py 的 detect_e_shapes 函数。
"""

import cv2
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)


def detect_e_shapes(
    image: np.ndarray,
    config_manager,
    min_area: int = None,
    max_area: int = None,
    aspect_ratio_range: Tuple[float, float] = None,
    solidity_range: Tuple[float, float] = None,
    horizontal_segments_count: int = 3,
    roi: Optional[Dict] = None,
    debug: bool = False
) -> Tuple[List, Dict]:
    """
    检测图像中的E字母形状
    
    Args:
        image: 输入的BGR格式图像
        config_manager: 配置管理器
        min_area: 最小轮廓面积
        max_area: 最大轮廓面积
        aspect_ratio_range: 宽高比范围
        solidity_range: 实心度范围
        horizontal_segments_count: 水平线段数量
        roi: 感兴趣区域
        debug: 是否开启调试模式
        
    Returns:
        Tuple[List, Dict]: (E字母列表, 中心线数据)
    """
    try:
        # 获取配置参数
        if min_area is None:
            min_area = config_manager.get(
                "analyzers.water_level.e_shape_detection.min_area", 100
            )
        if max_area is None:
            max_area = config_manager.get(
                "analyzers.water_level.e_shape_detection.max_area", 10000
            )
        if aspect_ratio_range is None:
            aspect_ratio_range = (
                config_manager.get("analyzers.water_level.e_shape_detection.aspect_ratio_min", 0.5),
                config_manager.get("analyzers.water_level.e_shape_detection.aspect_ratio_max", 2.0)
            )
        if solidity_range is None:
            solidity_range = (
                config_manager.get("analyzers.water_level.e_shape_detection.solidity_min", 0.4),
                config_manager.get("analyzers.water_level.e_shape_detection.solidity_max", 0.9)
            )
        
        logger.debug(f"E字母检测参数: min_area={min_area}, max_area={max_area}")
        logger.debug(f"宽高比范围: {aspect_ratio_range}, 实心度范围: {solidity_range}")
        
        # 应用ROI
        if roi:
            x, y, w, h = roi["x"], roi["y"], roi["width"], roi["height"]
            roi_image = image[y:y+h, x:x+w]
            offset_x, offset_y = x, y
        else:
            roi_image = image
            offset_x, offset_y = 0, 0
        
        # 图像预处理
        processed_image = _preprocess_image_for_e_detection(roi_image)
        
        # 查找轮廓
        contours, _ = cv2.findContours(processed_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        logger.debug(f"找到 {len(contours)} 个轮廓")
        
        # 筛选和分析轮廓
        e_letters = []
        for contour in contours:
            # 基本几何特征筛选
            if not _is_valid_contour_geometry(contour, min_area, max_area, aspect_ratio_range, solidity_range):
                continue
            
            # E字母形状特征检测
            e_type = _analyze_e_shape_features(contour, roi_image, horizontal_segments_count)
            if e_type is not None:
                # 获取边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 转换为全局坐标
                global_x = x + offset_x
                global_y = y + offset_y
                
                e_letters.append((global_x, global_y, w, h, e_type))
                
                logger.debug(f"检测到E字母: 位置=({global_x}, {global_y}), 尺寸=({w}, {h}), 类型={e_type}")
        
        logger.info(f"总共检测到 {len(e_letters)} 个E字母")
        
        # 生成中心线数据（简化实现）
        centerline_data = _generate_centerline_data(e_letters)
        
        return e_letters, centerline_data
        
    except Exception as e:
        logger.error(f"E字母检测失败: {str(e)}")
        return [], {}


def _preprocess_image_for_e_detection(image: np.ndarray) -> np.ndarray:
    """
    为E字母检测预处理图像
    
    Args:
        image: 输入图像
        
    Returns:
        np.ndarray: 预处理后的二值图像
    """
    try:
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 自适应阈值
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return binary
        
    except Exception as e:
        logger.error(f"图像预处理失败: {str(e)}")
        return image


def _is_valid_contour_geometry(
    contour: np.ndarray,
    min_area: int,
    max_area: int,
    aspect_ratio_range: Tuple[float, float],
    solidity_range: Tuple[float, float]
) -> bool:
    """
    检查轮廓的基本几何特征是否符合E字母要求
    
    Args:
        contour: 轮廓
        min_area: 最小面积
        max_area: 最大面积
        aspect_ratio_range: 宽高比范围
        solidity_range: 实心度范围
        
    Returns:
        bool: 是否符合要求
    """
    try:
        # 计算面积
        area = cv2.contourArea(contour)
        if area < min_area or area > max_area:
            return False
        
        # 计算边界框和宽高比
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio < aspect_ratio_range[0] or aspect_ratio > aspect_ratio_range[1]:
            return False
        
        # 计算实心度
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        solidity = area / hull_area if hull_area > 0 else 0
        if solidity < solidity_range[0] or solidity > solidity_range[1]:
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"几何特征检查失败: {str(e)}")
        return False


def _analyze_e_shape_features(
    contour: np.ndarray,
    image: np.ndarray,
    horizontal_segments_count: int
) -> Optional[str]:
    """
    分析轮廓的E字母形状特征
    
    Args:
        contour: 轮廓
        image: 图像
        horizontal_segments_count: 期望的水平线段数量
        
    Returns:
        Optional[str]: E字母类型 ("normal" 或 "inverted")，如果不是E字母则返回None
    """
    try:
        # 获取边界框
        x, y, w, h = cv2.boundingRect(contour)
        
        # 创建轮廓掩码
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        cv2.drawContours(mask, [contour], -1, 255, -1)
        
        # 分析水平投影
        roi_mask = mask[y:y+h, x:x+w]
        horizontal_projection = np.sum(roi_mask, axis=1)
        
        # 查找水平线段
        horizontal_segments = _find_horizontal_segments(horizontal_projection)
        
        # 分析垂直投影
        vertical_projection = np.sum(roi_mask, axis=0)
        
        # 检查是否符合E字母特征
        if len(horizontal_segments) >= horizontal_segments_count:
            # 进一步分析确定E字母类型
            e_type = _determine_e_type(horizontal_segments, vertical_projection, w, h)
            return e_type
        
        return None
        
    except Exception as e:
        logger.error(f"E字母形状特征分析失败: {str(e)}")
        return None


def _find_horizontal_segments(projection: np.ndarray) -> List[Tuple[int, int]]:
    """
    在投影中查找水平线段
    
    Args:
        projection: 水平投影数组
        
    Returns:
        List[Tuple[int, int]]: 线段的起始和结束位置列表
    """
    segments = []
    in_segment = False
    start = 0
    
    threshold = np.max(projection) * 0.3  # 阈值设为最大值的30%
    
    for i, value in enumerate(projection):
        if value > threshold and not in_segment:
            start = i
            in_segment = True
        elif value <= threshold and in_segment:
            segments.append((start, i))
            in_segment = False
    
    # 处理最后一个线段
    if in_segment:
        segments.append((start, len(projection)))
    
    return segments


def _determine_e_type(
    horizontal_segments: List[Tuple[int, int]],
    vertical_projection: np.ndarray,
    width: int,
    height: int
) -> Optional[str]:
    """
    根据线段分布确定E字母类型
    
    Args:
        horizontal_segments: 水平线段列表
        vertical_projection: 垂直投影
        width: 宽度
        height: 高度
        
    Returns:
        Optional[str]: E字母类型
    """
    try:
        # 简化的E字母类型判断
        # 这里可以根据具体需求实现更复杂的判断逻辑
        
        # 检查垂直投影的分布
        left_half = np.sum(vertical_projection[:width//2])
        right_half = np.sum(vertical_projection[width//2:])
        
        # 如果左半部分的投影值明显大于右半部分，可能是正向E
        if left_half > right_half * 1.5:
            return "normal"
        # 如果右半部分的投影值明显大于左半部分，可能是反向E
        elif right_half > left_half * 1.5:
            return "inverted"
        
        # 默认返回正向E
        return "normal"
        
    except Exception as e:
        logger.error(f"E字母类型判断失败: {str(e)}")
        return "normal"


def _generate_centerline_data(e_letters: List) -> Dict:
    """
    生成中心线数据
    
    Args:
        e_letters: E字母列表
        
    Returns:
        Dict: 中心线数据
    """
    try:
        if not e_letters:
            return {}
        
        # 计算E字母的平均中心线
        center_x_values = []
        for x, y, w, h, e_type in e_letters:
            center_x = x + w // 2
            center_x_values.append(center_x)
        
        avg_center_x = int(np.mean(center_x_values))
        
        return {
            "center_x": avg_center_x,
            "e_count": len(e_letters),
            "center_x_std": float(np.std(center_x_values)) if len(center_x_values) > 1 else 0.0
        }
        
    except Exception as e:
        logger.error(f"生成中心线数据失败: {str(e)}")
        return {}
