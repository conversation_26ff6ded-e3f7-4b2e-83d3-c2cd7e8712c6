"""
水尺检测算法

从旧项目移植的水尺检测功能，通过检测E字母来定位水尺位置。
基于 old_src/water_level/src/utils/water_rule_utils.py 的 detect_water_rule 函数。
"""

import cv2
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)


def detect_water_rule(
    image: np.n<PERSON><PERSON>,
    config_manager,
    roi_width_half: int = None,
    debug: bool = False
) -> Dict[str, Any]:
    """
    通过直接检测"E"形字母来定位水尺。
    
    Args:
        image: 输入的BGR格式图像
        config_manager: 配置管理器
        roi_width_half: ROI宽度的一半（像素），用于确定水尺区域的宽度
        debug: 是否开启调试模式
        
    Returns:
        Dict[str, Any]: 包含水尺ROI、标尺顶端坐标、所有E字母信息的结果字典
    """
    try:
        # 获取配置参数
        if roi_width_half is None:
            roi_width_half = config_manager.get(
                "analyzers.water_level.methods.ruler_detection.roi_width_half", 100
            )
        
        # 获取图像尺寸
        height, width = image.shape[:2]
        
        logger.debug(f"图像尺寸={width}x{height}")
        logger.debug(f"ROI宽度半径={roi_width_half}")
        
        # 检测E字母形状
        from .shape_recognition import detect_e_shapes
        
        # 在整个图像中统一检测所有E形字母
        all_e_letters_with_type, centerline_data = detect_e_shapes(
            image=image,
            config_manager=config_manager,
            min_area=100,
            max_area=10000,
            aspect_ratio_range=(0.5, 2.0),
            solidity_range=(0.4, 0.9),
            horizontal_segments_count=3,
            roi=None,
            debug=debug
        )
        
        # 如果没有检测到任何E字母，则返回失败
        if not all_e_letters_with_type:
            logger.warning("在图像中未找到正向或反向的E字母")
            return {
                "success": False,
                "roi": None,
                "ruler_top": None,
                "all_e_letters": [],
                "e_letters_count": 0,
                "error": "未检测到E字母"
            }
        
        logger.info(f"检测到 {len(all_e_letters_with_type)} 个E字母")
        
        # 分析E字母分布，确定水尺区域
        roi, ruler_top = _analyze_e_letter_distribution(
            all_e_letters_with_type, width, height, roi_width_half
        )
        
        if roi is None or ruler_top is None:
            logger.warning("无法根据E字母分布确定水尺区域")
            return {
                "success": False,
                "roi": None,
                "ruler_top": None,
                "all_e_letters": all_e_letters_with_type,
                "e_letters_count": len(all_e_letters_with_type),
                "error": "无法确定水尺区域"
            }
        
        logger.info(f"检测到水尺ROI: {roi}")
        logger.info(f"检测到水尺顶部: {ruler_top}")
        
        return {
            "success": True,
            "roi": roi,
            "ruler_top": ruler_top,
            "all_e_letters": all_e_letters_with_type,
            "e_letters_count": len(all_e_letters_with_type),
            "centerline_data": centerline_data
        }
        
    except Exception as e:
        logger.error(f"水尺检测失败: {str(e)}")
        return {
            "success": False,
            "roi": None,
            "ruler_top": None,
            "all_e_letters": [],
            "e_letters_count": 0,
            "error": str(e)
        }


def _analyze_e_letter_distribution(
    all_e_letters_with_type: List,
    image_width: int,
    image_height: int,
    roi_width_half: int
) -> Tuple[Optional[Dict], Optional[Tuple]]:
    """
    分析E字母分布，确定水尺区域和顶部位置
    
    Args:
        all_e_letters_with_type: 检测到的E字母列表
        image_width: 图像宽度
        image_height: 图像高度
        roi_width_half: ROI宽度的一半
        
    Returns:
        Tuple[Optional[Dict], Optional[Tuple]]: (ROI字典, 水尺顶部坐标)
    """
    if not all_e_letters_with_type:
        return None, None
    
    try:
        # 提取所有E字母的中心点
        e_centers = []
        for letter_info in all_e_letters_with_type:
            x, y, w, h, letter_type = letter_info[:5]
            center_x = x + w // 2
            center_y = y + h // 2
            e_centers.append((center_x, center_y))
        
        # 计算E字母的平均X坐标（水尺的水平位置）
        avg_x = int(np.mean([center[0] for center in e_centers]))
        
        # 找到最高的E字母（Y坐标最小）
        min_y = min([center[1] for center in e_centers])
        
        # 构建ROI
        roi_left = max(0, avg_x - roi_width_half)
        roi_right = min(image_width, avg_x + roi_width_half)
        roi_top = max(0, min_y - 50)  # 在最高E字母上方留一些空间
        roi_bottom = image_height
        
        roi = {
            "x": roi_left,
            "y": roi_top,
            "width": roi_right - roi_left,
            "height": roi_bottom - roi_top
        }
        
        # 水尺顶部位置（使用最高E字母的位置）
        ruler_top = (avg_x, min_y)
        
        return roi, ruler_top
        
    except Exception as e:
        logger.error(f"分析E字母分布失败: {str(e)}")
        return None, None


def update_roi_config(config_manager, detected_roi: Dict[str, int]):
    """
    更新配置文件中的default_roi
    
    Args:
        config_manager: 配置管理器
        detected_roi: 检测到的ROI区域
    """
    try:
        # 这里可以实现ROI配置的更新逻辑
        # 暂时只记录日志，实际实现需要根据配置管理器的接口
        logger.info(f"检测到新的ROI，建议更新配置: {detected_roi}")
        
        # TODO: 实现配置更新逻辑
        # config_manager.update_config("analyzers.water_level.default_roi", detected_roi)
        
    except Exception as e:
        logger.error(f"更新ROI配置失败: {str(e)}")


def validate_ruler_detection(
    roi: Dict[str, int],
    ruler_top: Tuple[int, int],
    all_e_letters: List,
    image_shape: Tuple[int, int]
) -> bool:
    """
    验证水尺检测结果的合理性
    
    Args:
        roi: 检测到的ROI区域
        ruler_top: 水尺顶部坐标
        all_e_letters: 所有E字母信息
        image_shape: 图像尺寸 (height, width)
        
    Returns:
        bool: 检测结果是否合理
    """
    try:
        height, width = image_shape[:2]
        
        # 检查ROI是否在图像范围内
        if (roi["x"] < 0 or roi["y"] < 0 or 
            roi["x"] + roi["width"] > width or 
            roi["y"] + roi["height"] > height):
            logger.warning("ROI超出图像范围")
            return False
        
        # 检查水尺顶部是否在ROI内
        if (ruler_top[0] < roi["x"] or ruler_top[0] > roi["x"] + roi["width"] or
            ruler_top[1] < roi["y"] or ruler_top[1] > roi["y"] + roi["height"]):
            logger.warning("水尺顶部不在ROI内")
            return False
        
        # 检查E字母数量是否合理
        if len(all_e_letters) < 2:
            logger.warning("E字母数量过少，可能检测不准确")
            return False
        
        logger.debug("水尺检测结果验证通过")
        return True
        
    except Exception as e:
        logger.error(f"验证水尺检测结果失败: {str(e)}")
        return False
