"""
像素比率计算算法

从旧项目移植的像素比率计算功能，基于E字母间距计算像素/厘米比率。
基于 old_src/water_level/src/utils/water_rule_utils.py 的 calculate_pixel_cm_ratio 函数。
"""

import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)


def calculate_pixel_cm_ratio(
    all_e_letters_with_type: List,
    water_surface_y: int = None,
    real_e_height_cm: float = 5.0,
    debug: bool = False
) -> Optional[float]:
    """
    基于E字母信息计算像素/厘米比率
    
    Args:
        all_e_letters_with_type: 所有E字母信息列表，格式为 [(x, y, w, h, type), ...]
        water_surface_y: 水面Y坐标（可选，用于筛选水面以上的E字母）
        real_e_height_cm: E字母的实际高度（厘米），默认5.0cm
        debug: 是否开启调试模式
        
    Returns:
        Optional[float]: 像素/厘米比率，如果计算失败则返回None
    """
    try:
        if not all_e_letters_with_type:
            logger.warning("没有E字母信息，无法计算像素比率")
            return None
        
        logger.debug(f"输入E字母数量: {len(all_e_letters_with_type)}")
        logger.debug(f"E字母实际高度: {real_e_height_cm}cm")
        
        # 筛选有效的E字母
        valid_letters = _filter_valid_letters(all_e_letters_with_type, water_surface_y)
        
        if not valid_letters:
            logger.warning("没有有效的E字母用于计算像素比率")
            return None
        
        logger.debug(f"有效E字母数量: {len(valid_letters)}")
        
        # 方法1: 基于E字母高度计算比率
        height_based_ratios = _calculate_height_based_ratios(valid_letters, real_e_height_cm)
        
        # 方法2: 基于E字母间距计算比率（如果有多个E字母）
        spacing_based_ratios = []
        if len(valid_letters) >= 2:
            spacing_based_ratios = _calculate_spacing_based_ratios(valid_letters, real_e_height_cm)
        
        # 综合计算最终比率
        final_ratio = _combine_ratios(height_based_ratios, spacing_based_ratios)
        
        if final_ratio is not None:
            logger.info(f"计算得到像素/厘米比率: {final_ratio:.2f}")
            
            if debug:
                logger.debug(f"高度基础比率: {height_based_ratios}")
                logger.debug(f"间距基础比率: {spacing_based_ratios}")
        
        return final_ratio
        
    except Exception as e:
        logger.error(f"计算像素比率失败: {str(e)}")
        return None


def _filter_valid_letters(
    all_e_letters: List,
    water_surface_y: Optional[int] = None
) -> List:
    """
    筛选有效的E字母
    
    Args:
        all_e_letters: 所有E字母列表
        water_surface_y: 水面Y坐标
        
    Returns:
        List: 有效的E字母列表
    """
    valid_letters = []
    
    for letter_info in all_e_letters:
        x, y, w, h, letter_type = letter_info[:5]
        
        # 基本尺寸检查
        if w <= 0 or h <= 0:
            continue
        
        # 如果提供了水面位置，只考虑水面以上的E字母
        if water_surface_y is not None:
            letter_bottom = y + h
            if letter_bottom >= water_surface_y:
                logger.debug(f"跳过水面以下的E字母: y={y}, h={h}, 水面y={water_surface_y}")
                continue
        
        # 宽高比检查（E字母通常比较方正）
        aspect_ratio = w / h
        if aspect_ratio < 0.3 or aspect_ratio > 3.0:
            logger.debug(f"跳过宽高比异常的E字母: 宽高比={aspect_ratio:.2f}")
            continue
        
        valid_letters.append(letter_info)
    
    return valid_letters


def _calculate_height_based_ratios(
    valid_letters: List,
    real_e_height_cm: float
) -> List[float]:
    """
    基于E字母高度计算像素比率
    
    Args:
        valid_letters: 有效的E字母列表
        real_e_height_cm: E字母实际高度
        
    Returns:
        List[float]: 基于高度的比率列表
    """
    ratios = []
    
    for letter_info in valid_letters:
        x, y, w, h, letter_type = letter_info[:5]
        
        # 像素高度 / 实际高度 = 像素/厘米比率
        ratio = h / real_e_height_cm
        ratios.append(ratio)
        
        logger.debug(f"E字母高度比率: 像素高度={h}, 实际高度={real_e_height_cm}cm, 比率={ratio:.2f}")
    
    return ratios


def _calculate_spacing_based_ratios(
    valid_letters: List,
    real_e_height_cm: float
) -> List[float]:
    """
    基于E字母间距计算像素比率
    
    Args:
        valid_letters: 有效的E字母列表
        real_e_height_cm: E字母实际高度
        
    Returns:
        List[float]: 基于间距的比率列表
    """
    ratios = []
    
    # 按Y坐标排序E字母（从上到下）
    sorted_letters = sorted(valid_letters, key=lambda x: x[1])
    
    # 计算相邻E字母之间的间距
    for i in range(len(sorted_letters) - 1):
        letter1 = sorted_letters[i]
        letter2 = sorted_letters[i + 1]
        
        # 计算中心点间距
        center1_y = letter1[1] + letter1[3] // 2
        center2_y = letter2[1] + letter2[3] // 2
        pixel_spacing = center2_y - center1_y
        
        # 理论上相邻E字母间距应该是5cm（一个E字母的高度）
        expected_spacing_cm = real_e_height_cm
        
        if pixel_spacing > 0:
            ratio = pixel_spacing / expected_spacing_cm
            ratios.append(ratio)
            
            logger.debug(f"E字母间距比率: 像素间距={pixel_spacing}, 期望间距={expected_spacing_cm}cm, 比率={ratio:.2f}")
    
    return ratios


def _combine_ratios(
    height_based_ratios: List[float],
    spacing_based_ratios: List[float]
) -> Optional[float]:
    """
    综合多种方法计算的比率，得到最终结果
    
    Args:
        height_based_ratios: 基于高度的比率列表
        spacing_based_ratios: 基于间距的比率列表
        
    Returns:
        Optional[float]: 最终的像素/厘米比率
    """
    try:
        all_ratios = []
        weights = []
        
        # 添加高度基础比率（权重较高，因为更可靠）
        for ratio in height_based_ratios:
            if ratio > 0:
                all_ratios.append(ratio)
                weights.append(2.0)  # 高权重
        
        # 添加间距基础比率（权重较低）
        for ratio in spacing_based_ratios:
            if ratio > 0:
                all_ratios.append(ratio)
                weights.append(1.0)  # 低权重
        
        if not all_ratios:
            return None
        
        # 计算加权平均
        if len(all_ratios) == 1:
            return all_ratios[0]
        
        # 去除异常值（使用四分位数方法）
        filtered_ratios, filtered_weights = _remove_outliers(all_ratios, weights)
        
        if not filtered_ratios:
            # 如果所有值都被认为是异常值，使用原始数据
            filtered_ratios = all_ratios
            filtered_weights = weights
        
        # 计算加权平均
        total_weight = sum(filtered_weights)
        if total_weight > 0:
            weighted_sum = sum(ratio * weight for ratio, weight in zip(filtered_ratios, filtered_weights))
            final_ratio = weighted_sum / total_weight
        else:
            final_ratio = np.mean(filtered_ratios)
        
        return final_ratio
        
    except Exception as e:
        logger.error(f"综合比率计算失败: {str(e)}")
        return None


def _remove_outliers(
    ratios: List[float],
    weights: List[float]
) -> Tuple[List[float], List[float]]:
    """
    使用四分位数方法去除异常值
    
    Args:
        ratios: 比率列表
        weights: 权重列表
        
    Returns:
        Tuple[List[float], List[float]]: (过滤后的比率, 过滤后的权重)
    """
    try:
        if len(ratios) <= 2:
            return ratios, weights
        
        # 计算四分位数
        q1 = np.percentile(ratios, 25)
        q3 = np.percentile(ratios, 75)
        iqr = q3 - q1
        
        # 定义异常值边界
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # 过滤异常值
        filtered_ratios = []
        filtered_weights = []
        
        for ratio, weight in zip(ratios, weights):
            if lower_bound <= ratio <= upper_bound:
                filtered_ratios.append(ratio)
                filtered_weights.append(weight)
            else:
                logger.debug(f"去除异常比率值: {ratio:.2f} (边界: {lower_bound:.2f} - {upper_bound:.2f})")
        
        return filtered_ratios, filtered_weights
        
    except Exception as e:
        logger.error(f"去除异常值失败: {str(e)}")
        return ratios, weights


def validate_pixel_cm_ratio(ratio: float) -> bool:
    """
    验证像素/厘米比率的合理性
    
    Args:
        ratio: 像素/厘米比率
        
    Returns:
        bool: 比率是否合理
    """
    try:
        # 合理的比率范围（根据实际情况调整）
        min_ratio = 1.0   # 最小1像素/厘米
        max_ratio = 100.0 # 最大100像素/厘米
        
        if ratio < min_ratio or ratio > max_ratio:
            logger.warning(f"像素比率超出合理范围: {ratio:.2f} (范围: {min_ratio} - {max_ratio})")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"验证像素比率失败: {str(e)}")
        return False


def get_ratio_confidence(
    height_based_ratios: List[float],
    spacing_based_ratios: List[float],
    final_ratio: float
) -> float:
    """
    计算比率计算的置信度
    
    Args:
        height_based_ratios: 基于高度的比率列表
        spacing_based_ratios: 基于间距的比率列表
        final_ratio: 最终比率
        
    Returns:
        float: 置信度 (0-1)
    """
    try:
        all_ratios = height_based_ratios + spacing_based_ratios
        
        if not all_ratios:
            return 0.0
        
        # 基于比率的一致性计算置信度
        std_dev = np.std(all_ratios)
        mean_ratio = np.mean(all_ratios)
        
        # 变异系数（标准差/均值）
        cv = std_dev / mean_ratio if mean_ratio > 0 else 1.0
        
        # 置信度与一致性成反比
        confidence = max(0.0, 1.0 - cv)
        
        # 基于样本数量调整置信度
        sample_bonus = min(0.2, len(all_ratios) * 0.05)
        confidence = min(1.0, confidence + sample_bonus)
        
        return confidence
        
    except Exception as e:
        logger.error(f"计算置信度失败: {str(e)}")
        return 0.5  # 默认中等置信度
