"""
测试服务层

封装TestRunner功能，提供异步测试执行接口，用于HTTP API调用。
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from ..utils.config_manager import ConfigManager
from ..testing.test_runner import TestRunner

logger = logging.getLogger(__name__)


class TestService:
    """测试服务，提供异步的算法测试接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化测试服务
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.workspace_root = Path(".").resolve()
        self.test_runner = TestRunner(self.workspace_root)
        
        # 获取测试配置
        self.test_config = self.config_manager.get_config("testing", {})
        self.default_test_data = self.test_config.get("default_test_data", {})
        self.execution_config = self.test_config.get("execution", {})
        
        logger.info("测试服务已初始化")
    
    async def test_algorithm(
        self, 
        algorithm: str, 
        case_names: Optional[List[str]] = None,
        custom_parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        异步测试指定算法
        
        Args:
            algorithm: 算法名称 (water_level, flow_speed, anomaly)
            case_names: 要运行的测试用例名称列表，None表示运行所有
            custom_parameters: 自定义测试参数
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        logger.info(f"开始异步测试算法: {algorithm}")
        
        try:
            # 验证算法名称
            if algorithm not in ["water_level", "flow_speed", "anomaly"]:
                raise ValueError(f"不支持的算法: {algorithm}")
            
            # 如果提供了自定义参数，创建临时测试用例
            if custom_parameters:
                await self._create_temporary_test_case(algorithm, custom_parameters)
            
            # 在线程池中运行测试（因为TestRunner是同步的）
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._run_algorithm_test_sync,
                algorithm,
                case_names
            )
            
            # 添加测试服务的元数据
            result["test_service_info"] = {
                "service_version": "1.0.0",
                "execution_mode": "async",
                "custom_parameters_used": custom_parameters is not None,
                "workspace_root": str(self.workspace_root)
            }
            
            logger.info(f"算法 {algorithm} 测试完成，成功率: {result.get('success_rate', 0):.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"算法 {algorithm} 测试失败: {str(e)}")
            return {
                "algorithm": algorithm,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "success_rate": 0.0,
                "total_cases": 0,
                "passed_cases": 0,
                "failed_cases": 0,
                "error_cases": 1
            }
    
    def _run_algorithm_test_sync(
        self, 
        algorithm: str, 
        case_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        同步运行算法测试（在线程池中执行）
        
        Args:
            algorithm: 算法名称
            case_names: 测试用例名称列表
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        return self.test_runner.run_algorithm_tests(
            algorithm=algorithm,
            case_names=case_names,
            generate_report=True,
            report_format="html"
        )
    
    async def _create_temporary_test_case(
        self, 
        algorithm: str, 
        custom_parameters: Dict[str, Any]
    ) -> None:
        """
        创建临时测试用例（用于自定义参数测试）
        
        Args:
            algorithm: 算法名称
            custom_parameters: 自定义参数
        """
        # 这里可以实现创建临时测试用例的逻辑
        # 目前先记录日志，后续可以扩展
        logger.info(f"为算法 {algorithm} 创建临时测试用例，参数: {custom_parameters}")
    
    async def test_all_algorithms(self) -> Dict[str, Any]:
        """
        异步测试所有算法
        
        Returns:
            Dict[str, Any]: 所有算法的测试结果
        """
        logger.info("开始测试所有算法")
        
        algorithms = ["water_level", "flow_speed", "anomaly"]
        results = {}
        overall_stats = {
            "total_algorithms": len(algorithms),
            "total_cases": 0,
            "passed_cases": 0,
            "failed_cases": 0,
            "error_cases": 0
        }
        
        for algorithm in algorithms:
            try:
                result = await self.test_algorithm(algorithm)
                results[algorithm] = result
                
                # 累计统计
                overall_stats["total_cases"] += result.get("total_cases", 0)
                overall_stats["passed_cases"] += result.get("passed_cases", 0)
                overall_stats["failed_cases"] += result.get("failed_cases", 0)
                overall_stats["error_cases"] += result.get("error_cases", 0)
                
            except Exception as e:
                logger.error(f"算法 {algorithm} 测试异常: {str(e)}")
                results[algorithm] = {
                    "status": "error",
                    "error": str(e),
                    "algorithm": algorithm
                }
                overall_stats["error_cases"] += 1
        
        # 计算总体成功率
        total_tests = overall_stats["total_cases"] + overall_stats["error_cases"]
        if total_tests > 0:
            overall_stats["overall_success_rate"] = (
                overall_stats["passed_cases"] / total_tests * 100
            )
        else:
            overall_stats["overall_success_rate"] = 0.0
        
        return {
            "status": "completed",
            "timestamp": datetime.now().isoformat(),
            "algorithms": results,
            "overall_summary": overall_stats
        }
    
    async def list_available_algorithms(self) -> List[str]:
        """
        获取支持的算法列表
        
        Returns:
            List[str]: 算法名称列表
        """
        return ["water_level", "flow_speed", "anomaly"]
    
    async def list_test_cases(self, algorithm: str) -> List[str]:
        """
        获取指定算法的测试用例列表
        
        Args:
            algorithm: 算法名称
            
        Returns:
            List[str]: 测试用例名称列表
        """
        try:
            loop = asyncio.get_event_loop()
            cases = await loop.run_in_executor(
                None,
                self.test_runner.list_test_cases,
                algorithm
            )
            return cases
        except Exception as e:
            logger.error(f"获取算法 {algorithm} 的测试用例失败: {str(e)}")
            return []
    
    async def get_default_test_parameters(self, algorithm: str) -> Dict[str, Any]:
        """
        获取算法的默认测试参数
        
        Args:
            algorithm: 算法名称
            
        Returns:
            Dict[str, Any]: 默认测试参数
        """
        return self.default_test_data.get(algorithm, {})
    
    async def cleanup(self) -> None:
        """清理测试服务资源"""
        logger.info("正在清理测试服务资源...")
        # 这里可以添加清理逻辑，比如清理临时文件等
        logger.info("测试服务资源清理完成")
