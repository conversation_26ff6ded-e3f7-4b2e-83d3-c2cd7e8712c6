"""
测试数据管理器

负责加载、验证和管理测试用例数据。
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self, test_cases_dir: Path):
        self.test_cases_dir = test_cases_dir
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        
    def load_test_cases(self, algorithm: str) -> Dict[str, Any]:
        """
        加载指定算法的测试用例
        
        Args:
            algorithm: 算法名称
            
        Returns:
            Dict[str, Any]: 测试用例数据
        """
        test_file = self.test_cases_dir / f"{algorithm}_test_cases.yaml"
        
        if not test_file.exists():
            logger.warning(f"测试用例文件不存在: {test_file}")
            return {"metadata": {}, "test_cases": []}
            
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                
            logger.info(f"成功加载测试用例: {test_file}")
            return data
            
        except Exception as e:
            logger.error(f"加载测试用例失败: {test_file}, 错误: {str(e)}")
            raise
    
    def validate_test_case(self, test_case: Dict[str, Any]) -> bool:
        """
        验证测试用例数据的完整性
        
        Args:
            test_case: 测试用例数据
            
        Returns:
            bool: 是否有效
        """
        required_fields = ["name", "image_path", "expected_results", "test_parameters"]
        
        for field in required_fields:
            if field not in test_case:
                logger.error(f"测试用例缺少必需字段: {field}")
                return False
                
        # 验证图片文件是否存在
        image_path = Path(test_case["image_path"])
        if not image_path.exists():
            logger.error(f"测试图片不存在: {image_path}")
            return False
            
        return True
    
    def filter_test_cases(self, test_cases: List[Dict[str, Any]], 
                         case_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        过滤测试用例
        
        Args:
            test_cases: 所有测试用例
            case_names: 要运行的测试用例名称列表，None表示运行所有
            
        Returns:
            List[Dict[str, Any]]: 过滤后的测试用例
        """
        if case_names is None:
            return test_cases
            
        filtered_cases = []
        for test_case in test_cases:
            if test_case["name"] in case_names:
                filtered_cases.append(test_case)
                
        return filtered_cases
    
    def save_test_results(self, algorithm: str, results: List[Dict[str, Any]], 
                         output_dir: Path) -> Path:
        """
        保存测试结果
        
        Args:
            algorithm: 算法名称
            results: 测试结果列表
            output_dir: 输出目录
            
        Returns:
            Path: 保存的文件路径
        """
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = output_dir / f"{algorithm}_test_results_{timestamp}.yaml"
        
        # 计算统计信息
        total_cases = len(results)
        passed_cases = sum(1 for r in results if r["status"] == "PASSED")
        failed_cases = sum(1 for r in results if r["status"] == "FAILED")
        error_cases = sum(1 for r in results if r["status"] == "ERROR")
        success_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0
        
        # 构建结果数据
        result_data = {
            "test_session": {
                "timestamp": datetime.now().isoformat(),
                "algorithm": algorithm,
                "total_cases": total_cases,
                "passed_cases": passed_cases,
                "failed_cases": failed_cases,
                "error_cases": error_cases,
                "success_rate": round(success_rate, 2)
            },
            "test_results": results
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(result_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                
            logger.info(f"测试结果已保存: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存测试结果失败: {str(e)}")
            raise
    
    def create_default_test_cases(self, algorithm: str) -> Path:
        """
        创建默认的测试用例文件
        
        Args:
            algorithm: 算法名称
            
        Returns:
            Path: 创建的文件路径
        """
        test_file = self.test_cases_dir / f"{algorithm}_test_cases.yaml"
        
        if algorithm == "water_level":
            default_data = {
                "metadata": {
                    "name": "水位识别测试用例集",
                    "version": "1.0",
                    "description": "用于验证水位识别算法准确性的测试用例"
                },
                "test_cases": [
                    {
                        "name": "water_level_case_1",
                        "description": "标准水位测试 - 1.9米水深",
                        "image_path": "data/temp/water_level.jpg",
                        "expected_results": {
                            "water_depth_m": 1.9,
                            "ruler_top_y": 1520,
                            "water_surface_y": 4000,
                            "tolerance": {
                                "water_depth_m": 0.1,
                                "ruler_top_y": 20,
                                "water_surface_y": 30
                            }
                        },
                        "test_parameters": {
                            "method": "transparency",
                            "roi": None,
                            "brightness_thresh": 120
                        }
                    },
                    {
                        "name": "water_level_case_2", 
                        "description": "标准水位测试 - 2.3米水深",
                        "image_path": "data/temp/water_level2.jpg",
                        "expected_results": {
                            "water_depth_m": 2.3,
                            "ruler_top_y": 110,
                            "water_surface_y": 1220,
                            "tolerance": {
                                "water_depth_m": 0.1,
                                "ruler_top_y": 20,
                                "water_surface_y": 30
                            }
                        },
                        "test_parameters": {
                            "method": "transparency",
                            "roi": None,
                            "brightness_thresh": 120
                        }
                    }
                ]
            }
        else:
            # 其他算法的默认模板
            default_data = {
                "metadata": {
                    "name": f"{algorithm}测试用例集",
                    "version": "1.0",
                    "description": f"用于验证{algorithm}算法的测试用例"
                },
                "test_cases": []
            }
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                yaml.dump(default_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                
            logger.info(f"已创建默认测试用例文件: {test_file}")
            return test_file
            
        except Exception as e:
            logger.error(f"创建默认测试用例文件失败: {str(e)}")
            raise
