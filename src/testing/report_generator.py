"""
测试报告生成器

生成详细的HTML、YAML或JSON格式的测试报告。
"""

import json
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

logger = logging.getLogger(__name__)


class ReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, output_dir: Path):
        """
        初始化报告生成器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_report(self, algorithm: str, results: List[Dict[str, Any]], 
                       summary: Dict[str, Any], format: str = "html") -> Path:
        """
        生成测试报告
        
        Args:
            algorithm: 算法名称
            results: 测试结果列表
            summary: 测试摘要
            format: 报告格式 ("html", "yaml", "json")
            
        Returns:
            Path: 生成的报告文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format.lower() == "html":
            return self._generate_html_report(algorithm, results, summary, timestamp)
        elif format.lower() == "yaml":
            return self._generate_yaml_report(algorithm, results, summary, timestamp)
        elif format.lower() == "json":
            return self._generate_json_report(algorithm, results, summary, timestamp)
        else:
            raise ValueError(f"不支持的报告格式: {format}")
    
    def _generate_html_report(self, algorithm: str, results: List[Dict[str, Any]], 
                             summary: Dict[str, Any], timestamp: str) -> Path:
        """生成HTML格式报告"""
        report_file = self.output_dir / f"{algorithm}_test_report_{timestamp}.html"
        
        # 计算统计数据
        passed_count = summary["passed_cases"]
        failed_count = summary["failed_cases"]
        error_count = summary["error_cases"]
        success_rate = summary["success_rate"]
        
        # 生成HTML内容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{algorithm} 算法测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 30px; }}
        .stats {{ display: flex; justify-content: space-around; margin: 20px 0; }}
        .stat-item {{ text-align: center; }}
        .stat-number {{ font-size: 2em; font-weight: bold; }}
        .passed {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .error {{ color: #fd7e14; }}
        .test-case {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .test-case.passed {{ border-left: 5px solid #28a745; }}
        .test-case.failed {{ border-left: 5px solid #dc3545; }}
        .test-case.error {{ border-left: 5px solid #fd7e14; }}
        .test-details {{ margin-top: 10px; }}
        .error-details {{ background-color: #f8d7da; padding: 10px; border-radius: 3px; margin-top: 10px; }}
        .result-table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
        .result-table th, .result-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .result-table th {{ background-color: #f2f2f2; }}
        .visualization {{ margin-top: 15px; }}
        .visualization img {{ max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{algorithm} 算法测试报告</h1>
            <p>生成时间: {summary['timestamp']}</p>
            <p><strong>使用真实处理器代码进行测试</strong></p>
        </div>
        
        <div class="summary">
            <h2>测试摘要</h2>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">{summary['total_cases']}</div>
                    <div>总测试用例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number passed">{passed_count}</div>
                    <div>通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number failed">{failed_count}</div>
                    <div>失败</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number error">{error_count}</div>
                    <div>错误</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{success_rate:.1f}%</div>
                    <div>成功率</div>
                </div>
            </div>
            <p><strong>平均执行时间:</strong> {summary['avg_execution_time']:.2f} 秒</p>
            <p><strong>测试状态:</strong> {summary['status']}</p>
        </div>
        
        <div class="test-results">
            <h2>详细测试结果</h2>
"""
        
        # 添加每个测试用例的详细结果
        for result in results:
            status_class = result['status'].lower()
            html_content += f"""
            <div class="test-case {status_class}">
                <h3>{result['case_name']} - {result['status']}</h3>
                <p><strong>执行时间:</strong> {result['execution_time']:.2f} 秒</p>
                
                {self._generate_result_table_html(result)}
                
                {self._generate_error_section_html(result)}
                
                {self._generate_visualization_section_html(result)}
            </div>
"""
        
        html_content += """
        </div>
    </div>
</body>
</html>
"""
        
        # 写入文件
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML报告已生成: {report_file}")
        return report_file
    
    def _generate_result_table_html(self, result: Dict[str, Any]) -> str:
        """生成结果对比表格的HTML"""
        if not result.get('actual_results') or not result.get('expected_results'):
            return ""
        
        actual = result['actual_results']
        expected = result['expected_results']
        errors = result.get('errors', {})
        
        html = """
        <div class="test-details">
            <h4>结果对比</h4>
            <table class="result-table">
                <tr><th>指标</th><th>期望值</th><th>实际值</th><th>误差</th></tr>
"""
        
        # 水深对比
        if 'water_depth_m' in expected:
            expected_depth = expected['water_depth_m']
            actual_depth = actual.get('water_depth_m', 0)
            error = errors.get('water_depth_m', 0)
            html += f"""
                <tr>
                    <td>水深 (米)</td>
                    <td>{expected_depth:.3f}</td>
                    <td>{actual_depth:.3f}</td>
                    <td>{error:+.3f}</td>
                </tr>
"""
        
        # 水面位置对比
        if 'water_surface_y' in expected:
            expected_y = expected['water_surface_y']
            actual_y = actual.get('water_surface_y', 0)
            error = errors.get('water_surface_y', 0)
            html += f"""
                <tr>
                    <td>水面位置 (像素)</td>
                    <td>{expected_y}</td>
                    <td>{actual_y}</td>
                    <td>{error:+d}</td>
                </tr>
"""
        
        # 标尺顶部位置对比
        if 'ruler_top_y' in expected:
            expected_ruler = expected['ruler_top_y']
            actual_ruler = actual.get('ruler_top_y', 0)
            error = errors.get('ruler_top_y', 0)
            html += f"""
                <tr>
                    <td>标尺顶部 (像素)</td>
                    <td>{expected_ruler}</td>
                    <td>{actual_ruler}</td>
                    <td>{error:+d}</td>
                </tr>
"""
        
        html += """
            </table>
        </div>
"""
        return html
    
    def _generate_error_section_html(self, result: Dict[str, Any]) -> str:
        """生成错误信息部分的HTML"""
        if result['status'] != 'ERROR' or not result.get('error_message'):
            return ""
        
        return f"""
        <div class="error-details">
            <h4>错误信息</h4>
            <p>{result['error_message']}</p>
        </div>
"""
    
    def _generate_visualization_section_html(self, result: Dict[str, Any]) -> str:
        """生成可视化部分的HTML"""
        if not result.get('output_files'):
            return ""
        
        html = """
        <div class="visualization">
            <h4>可视化结果</h4>
"""
        
        for file_path in result['output_files']:
            file_name = Path(file_path).name
            # 使用相对路径
            relative_path = Path(file_path).name
            html += f'<img src="{relative_path}" alt="{file_name}" title="{file_name}"><br>'
        
        html += "</div>"
        return html
    
    def _generate_yaml_report(self, algorithm: str, results: List[Dict[str, Any]], 
                             summary: Dict[str, Any], timestamp: str) -> Path:
        """生成YAML格式报告"""
        report_file = self.output_dir / f"{algorithm}_test_report_{timestamp}.yaml"
        
        report_data = {
            "test_report": {
                "algorithm": algorithm,
                "summary": summary,
                "test_results": results
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            yaml.dump(report_data, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        logger.info(f"YAML报告已生成: {report_file}")
        return report_file
    
    def _generate_json_report(self, algorithm: str, results: List[Dict[str, Any]], 
                             summary: Dict[str, Any], timestamp: str) -> Path:
        """生成JSON格式报告"""
        report_file = self.output_dir / f"{algorithm}_test_report_{timestamp}.json"
        
        report_data = {
            "test_report": {
                "algorithm": algorithm,
                "summary": summary,
                "test_results": results
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"JSON报告已生成: {report_file}")
        return report_file
