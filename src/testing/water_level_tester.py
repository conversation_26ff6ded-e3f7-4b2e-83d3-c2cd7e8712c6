"""
水位识别测试器

调用现有的水位识别处理器进行测试，确保测试的是真实工作环境中使用的算法。
"""

import cv2
import numpy as np
import logging
import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

from .algorithm_tester import AlgorithmTester, TestResult
from ..processors.water_level_processor import WaterLevelProcessor
from ..utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class WaterLevelTester(AlgorithmTester):
    """水位识别算法测试器"""
    
    def __init__(self, output_dir: Path):
        super().__init__("water_level", output_dir)
        
        # 初始化配置管理器和处理器
        self.config_manager = ConfigManager()
        self.processor = WaterLevelProcessor(self.config_manager)
        
        logger.info("水位识别测试器已初始化，使用真实的处理器代码")
    
    def run_algorithm(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行水位识别算法
        
        Args:
            test_case: 测试用例数据
            
        Returns:
            Dict[str, Any]: 算法执行结果
        """
        image_path = test_case["image_path"]
        test_params = test_case["test_parameters"]
        
        # 检查图像文件是否存在
        if not Path(image_path).exists():
            raise ValueError(f"测试图像不存在: {image_path}")
        
        logger.info(f"开始分析图像: {image_path}")
        
        # 准备参数
        method = test_params.get("method", "transparency")
        roi = test_params.get("roi")  # None表示自动检测
        task_id = f"test_{test_case['name']}"
        
        try:
            # 调用真实的处理器进行图片分析
            # 使用同步版本以避免事件循环问题
            result = self._run_processor_sync(image_path, method, roi, task_id)
            
            if result.get("success", False):
                # 处理器返回的结果格式可能需要适配
                return self._adapt_processor_result(result, image_path)
            else:
                return {
                    "success": False,
                    "error": result.get("error", "算法执行失败"),
                    "water_depth_m": 0.0,
                    "method_used": method,
                    "confidence": 0.0
                }
                
        except Exception as e:
            logger.error(f"算法执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "water_depth_m": 0.0,
                "method_used": method,
                "confidence": 0.0
            }
    
    def _run_processor_sync(self, image_path: str, method: str, roi: Optional[Dict], task_id: str) -> Dict[str, Any]:
        """
        同步运行处理器
        
        Args:
            image_path: 图片路径
            method: 检测方法
            roi: ROI区域
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 处理器结果
        """
        try:
            # 尝试使用现有的事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果在异步环境中，需要特殊处理
                # 创建新的事件循环在线程中运行
                import concurrent.futures
                import threading
                
                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(
                            self.processor.analyze_image(image_path, method, roi, task_id)
                        )
                    finally:
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
            else:
                # 直接运行
                return loop.run_until_complete(
                    self.processor.analyze_image(image_path, method, roi, task_id)
                )
        except RuntimeError:
            # 没有事件循环，创建新的
            return asyncio.run(
                self.processor.analyze_image(image_path, method, roi, task_id)
            )
    
    def _adapt_processor_result(self, processor_result: Dict[str, Any], image_path: str) -> Dict[str, Any]:
        """
        适配处理器结果格式
        
        Args:
            processor_result: 处理器原始结果
            image_path: 图片路径
            
        Returns:
            Dict[str, Any]: 适配后的结果
        """
        # 处理器返回的结果格式可能是：
        # {
        #     "success": True,
        #     "water_depth_cm": 125.5,
        #     "confidence": 0.95,
        #     "method_used": "transparency",
        #     "avg_waterline_y": 2914,
        #     "pixels_per_cm": None,
        #     ...
        # }
        
        # 适配为测试期望的格式
        adapted_result = {
            "success": processor_result.get("success", False),
            "method_used": processor_result.get("method_used", "unknown"),
            "confidence": processor_result.get("confidence", 0.0),
            "image_path": image_path
        }
        
        # 水深转换
        if "water_depth_cm" in processor_result:
            adapted_result["water_depth_cm"] = processor_result["water_depth_cm"]
            adapted_result["water_depth_m"] = processor_result["water_depth_cm"] / 100.0
        else:
            adapted_result["water_depth_cm"] = 0.0
            adapted_result["water_depth_m"] = 0.0
        
        # 水面位置
        if "avg_waterline_y" in processor_result:
            adapted_result["water_surface_y"] = int(processor_result["avg_waterline_y"])
        else:
            adapted_result["water_surface_y"] = 0
        
        # 标尺信息（处理器可能没有返回，需要从算法中获取）
        adapted_result["ruler_top_y"] = processor_result.get("ruler_top_y", 0)
        adapted_result["pixels_per_cm"] = processor_result.get("pixels_per_cm")
        adapted_result["exposed_length_cm"] = processor_result.get("exposed_length_cm", 0)
        
        # 其他信息
        adapted_result["frames_analyzed"] = processor_result.get("frames_analyzed", 1)
        adapted_result["valid_detections"] = processor_result.get("valid_detections", 0)
        adapted_result["detection_rate"] = processor_result.get("detection_rate", 0.0)
        
        return adapted_result
    
    def validate_results(self, actual: Dict[str, Any], expected: Dict[str, Any], 
                        tolerance: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        验证水位识别结果
        
        Args:
            actual: 实际结果
            expected: 期望结果
            tolerance: 容差配置
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否通过, 误差详情)
        """
        if not actual.get("success", False):
            return False, {"error": "算法执行失败"}
        
        errors = {}
        within_tolerance = True
        
        # 检查水深误差
        if "water_depth_m" in expected:
            actual_depth = actual.get("water_depth_m", 0.0)
            expected_depth = expected["water_depth_m"]
            depth_error = abs(actual_depth - expected_depth)
            depth_tolerance = tolerance.get("water_depth_m", 0.1)
            
            errors["water_depth_m"] = actual_depth - expected_depth
            if depth_error > depth_tolerance:
                within_tolerance = False
                logger.warning(f"水深误差超出容差: {depth_error:.3f}m > {depth_tolerance:.3f}m")
        
        # 检查水面位置误差
        if "water_surface_y" in expected:
            actual_y = actual.get("water_surface_y", 0)
            expected_y = expected["water_surface_y"]
            y_error = abs(actual_y - expected_y)
            y_tolerance = tolerance.get("water_surface_y", 30)
            
            errors["water_surface_y"] = actual_y - expected_y
            if y_error > y_tolerance:
                within_tolerance = False
                logger.warning(f"水面位置误差超出容差: {y_error}px > {y_tolerance}px")
        
        # 检查标尺顶部位置误差
        if "ruler_top_y" in expected:
            actual_ruler_y = actual.get("ruler_top_y", 0)
            expected_ruler_y = expected["ruler_top_y"]
            if actual_ruler_y is not None:
                ruler_error = abs(actual_ruler_y - expected_ruler_y)
                ruler_tolerance = tolerance.get("ruler_top_y", 20)
                
                errors["ruler_top_y"] = actual_ruler_y - expected_ruler_y
                if ruler_error > ruler_tolerance:
                    within_tolerance = False
                    logger.warning(f"标尺位置误差超出容差: {ruler_error}px > {ruler_tolerance}px")
        
        return within_tolerance, errors

    def generate_visualization(self, test_case: Dict[str, Any],
                             result: TestResult) -> List[str]:
        """
        生成可视化结果

        Args:
            test_case: 测试用例数据
            result: 测试结果

        Returns:
            List[str]: 生成的文件路径列表
        """
        output_files = []

        try:
            # 读取原始图像
            image_path = test_case["image_path"]
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图像用于可视化: {image_path}")
                return output_files

            # 创建可视化图像
            vis_image = image.copy()

            # 获取结果数据
            actual_results = result.actual_results
            expected_results = result.expected_results

            # 绘制期望的水面线（绿色）
            if "water_surface_y" in expected_results:
                expected_y = expected_results["water_surface_y"]
                cv2.line(vis_image, (0, expected_y), (vis_image.shape[1], expected_y),
                        (0, 255, 0), 3)
                cv2.putText(vis_image, f"Expected Water Surface (y={expected_y})",
                           (10, expected_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 绘制实际检测的水面线（蓝色）
            if actual_results.get("success") and "water_surface_y" in actual_results:
                actual_y = actual_results["water_surface_y"]
                cv2.line(vis_image, (0, actual_y), (vis_image.shape[1], actual_y),
                        (255, 0, 0), 2)
                cv2.putText(vis_image, f"Detected Water Surface (y={actual_y})",
                           (10, actual_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

            # 绘制期望的标尺顶部（绿色圆圈）
            if "ruler_top_y" in expected_results:
                expected_ruler_y = expected_results["ruler_top_y"]
                # 假设标尺在图像中央
                ruler_x = vis_image.shape[1] // 2
                cv2.circle(vis_image, (ruler_x, expected_ruler_y), 8, (0, 255, 0), -1)
                cv2.putText(vis_image, f"Expected Ruler Top (y={expected_ruler_y})",
                           (ruler_x + 15, expected_ruler_y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 绘制实际检测的标尺顶部（蓝色圆圈）
            if actual_results.get("success") and actual_results.get("ruler_top_y") is not None:
                actual_ruler_y = actual_results["ruler_top_y"]
                ruler_x = vis_image.shape[1] // 2
                cv2.circle(vis_image, (ruler_x, actual_ruler_y), 6, (255, 0, 0), -1)
                cv2.putText(vis_image, f"Detected Ruler Top (y={actual_ruler_y})",
                           (ruler_x + 15, actual_ruler_y + 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

            # 添加测试结果信息
            info_y = 30
            cv2.putText(vis_image, f"Test Case: {test_case['name']}",
                       (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

            info_y += 30
            status_color = (0, 255, 0) if result.status == "PASSED" else (0, 0, 255)
            cv2.putText(vis_image, f"Status: {result.status}",
                       (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)

            if actual_results.get("success"):
                info_y += 30
                expected_depth = expected_results.get("water_depth_m", 0)
                actual_depth = actual_results.get("water_depth_m", 0)
                cv2.putText(vis_image, f"Expected Depth: {expected_depth:.2f}m",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                info_y += 25
                cv2.putText(vis_image, f"Actual Depth: {actual_depth:.2f}m",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

                info_y += 25
                depth_error = abs(actual_depth - expected_depth)
                cv2.putText(vis_image, f"Depth Error: {depth_error:.3f}m",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

                # 显示算法信息
                info_y += 25
                method = actual_results.get("method_used", "unknown")
                confidence = actual_results.get("confidence", 0.0)
                cv2.putText(vis_image, f"Method: {method}, Confidence: {confidence:.2f}",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 保存可视化结果
            vis_filename = f"{test_case['name']}_visualization.jpg"
            vis_path = self.output_dir / vis_filename
            cv2.imwrite(str(vis_path), vis_image)
            output_files.append(str(vis_path))

            logger.info(f"已生成可视化结果: {vis_path}")

        except Exception as e:
            logger.error(f"生成可视化失败: {str(e)}")

        return output_files
