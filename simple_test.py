#!/usr/bin/env python3
"""
简单功能测试

测试VR视频分析系统的核心组件是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_config_and_logger():
    """测试配置管理器和日志系统"""
    print("测试配置管理器和日志系统...")
    
    try:
        # 设置配置文件路径
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        
        from utils.config_manager import ConfigManager
        from utils.logger import setup_logger
        
        # 初始化日志
        setup_logger(debug=True)
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 测试配置获取
        server_config = config_manager.get("server")
        print(f"✓ 服务器配置: {server_config}")
        
        # 测试嵌套配置
        water_level_enabled = config_manager.get("analyzers.water_level.enabled")
        print(f"✓ 水位分析器启用: {water_level_enabled}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置和日志测试失败: {e}")
        return False

def test_video_and_image_utils():
    """测试视频和图像工具"""
    print("\n测试视频和图像工具...")
    
    try:
        from utils.config_manager import ConfigManager
        from utils.video_utils import VideoStreamProcessor, sort_polygon_points_clockwise
        from utils.image_utils import ImageProcessor
        
        config_manager = ConfigManager()
        
        # 测试视频处理器创建
        video_processor = VideoStreamProcessor(config_manager)
        print("✓ 视频处理器创建成功")
        
        # 测试图像处理器创建
        image_processor = ImageProcessor(config_manager)
        print("✓ 图像处理器创建成功")
        
        # 测试多边形点排序
        points = [(0, 0), (1, 0), (1, 1), (0, 1)]
        sorted_points = sort_polygon_points_clockwise(points)
        print(f"✓ 多边形点排序: {len(sorted_points)} 个点")
        
        return True
        
    except Exception as e:
        print(f"✗ 视频和图像工具测试失败: {e}")
        return False

def test_water_level_algorithms():
    """测试水位识别算法"""
    print("\n测试水位识别算法...")
    
    try:
        import numpy as np
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[50:, :] = [100, 150, 200]  # 模拟水面
        
        # 测试透明度检测
        try:
            from algorithms.water_level.transparency import detect_waterline_transparency
            result = detect_waterline_transparency(test_image, config_manager, debug=False)
            print(f"✓ 透明度检测: 结果={result}")
        except Exception as e:
            print(f"✗ 透明度检测失败: {e}")
        
        # 测试梯度检测
        try:
            from algorithms.water_level.gradient import detect_waterline_gradient
            test_gray = np.mean(test_image, axis=2).astype(np.uint8)
            result = detect_waterline_gradient(test_gray, config_manager, debug=False)
            print(f"✓ 梯度检测: 结果={result}")
        except Exception as e:
            print(f"✗ 梯度检测失败: {e}")
        
        # 测试霍夫变换检测
        try:
            from algorithms.water_level.hough import detect_waterline_hough
            test_gray = np.mean(test_image, axis=2).astype(np.uint8)
            result = detect_waterline_hough(test_gray, config_manager, debug=False)
            print(f"✓ 霍夫变换检测: 结果={result}")
        except Exception as e:
            print(f"✗ 霍夫变换检测失败: {e}")
        
        # 测试颜色阈值检测
        try:
            from algorithms.water_level.color_threshold import detect_waterline_color_threshold
            result = detect_waterline_color_threshold(test_image, config_manager, debug=False)
            print(f"✓ 颜色阈值检测: 结果={result}")
        except Exception as e:
            print(f"✗ 颜色阈值检测失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 水位算法测试失败: {e}")
        return False

def test_flow_speed_algorithms():
    """测试流速识别算法"""
    print("\n测试流速识别算法...")
    
    try:
        import numpy as np
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建测试帧序列
        test_frames = []
        for i in range(5):
            frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            test_frames.append(frame)
        
        # 测试OTV算法
        try:
            from algorithms.flow_speed.otv import analyze_flow_otv
            result = analyze_flow_otv(
                video_frames=test_frames,
                roi_points=None,
                pixel_to_meter=0.01,
                fps=30.0,
                config_manager=config_manager,
                debug=False
            )
            print(f"✓ OTV算法: 平均流速={result.avg_speed:.3f}m/s")
        except Exception as e:
            print(f"✗ OTV算法失败: {e}")
        
        # 测试PIV算法（可能失败，因为需要OpenPIV）
        try:
            from algorithms.flow_speed.piv import analyze_flow_piv
            result = analyze_flow_piv(
                video_frames=test_frames,
                roi_points=None,
                pixel_to_meter=0.01,
                config_manager=config_manager,
                debug=False
            )
            print(f"✓ PIV算法: 平均流速={result.avg_speed:.3f}m/s")
        except Exception as e:
            print(f"✗ PIV算法失败（可能缺少OpenPIV库）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 流速算法测试失败: {e}")
        return False

def test_anomaly_algorithms():
    """测试异常检测算法"""
    print("\n测试异常检测算法...")
    
    try:
        import numpy as np
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建测试帧序列
        test_frames = []
        for i in range(3):
            frame = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            test_frames.append(frame)
        
        # 测试YOLO检测（可能失败，因为需要Ultralytics）
        try:
            from algorithms.anomaly.yolo_detector import detect_anomalies_yolo
            result = detect_anomalies_yolo(
                video_frames=test_frames,
                detection_targets=["person"],
                confidence_threshold=0.5,
                config_manager=config_manager,
                debug=False
            )
            print(f"✓ YOLO检测: 检测到{result.total_detections}个目标")
        except Exception as e:
            print(f"✗ YOLO检测失败（可能缺少Ultralytics库）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 异常检测算法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("VR视频分析系统简单功能测试")
    print("=" * 50)
    
    tests = [
        test_config_and_logger,
        test_video_and_image_utils,
        test_water_level_algorithms,
        test_flow_speed_algorithms,
        test_anomaly_algorithms
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 至少通过3个基础测试
        print("🎉 核心功能基本正常！")
        return True
    else:
        print("⚠️  核心功能存在问题，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
