# 水平面检测配置文件

# 图像预处理参数
image_preprocessing:
  blur_kernel_size: 5  # 高斯模糊核大小
  morph_kernel_size: 3  # 形态学操作核大小

# 梯度法参数
gradient:
  grad_thresh: 25  # 梯度阈值

# 霍夫变换法参数
hough:
  thresh: 70  # 霍夫变换阈值
  min_line_len_factor: 0.3  # 最小线长因子（相对于ROI宽度）
  max_gap: 20  # 最大线段间隙

# 颜色阈值法参数
color_threshold:
  hsv_lower: [35, 40, 40]  # 水体HSV下限
  hsv_upper: [85, 255, 255]  # 水体HSV上限
  morph_kernel_size: 5  # 形态学操作核大小
  min_area_factor: 0.01  # 最小区域因子

# 透明度检测法参数
transparency:
  alpha_thresh: 50  # 透明度检测阈值
  canny_low: 30  # Canny边缘检测低阈值
  canny_high: 100  # Canny边缘检测高阈值
  blur_ksize: 5  # 高斯模糊核大小
  morph_kernel_size: 5  # 形态学操作核大小
  min_area_factor: 0.01  # 最小区域因子
  brightness_thresh: 80  # 亮度阈值
  black_pixel_ratio_thresh: 0.5  # 黑色像素占比阈值
  avg_change_rate_thresh: 0.4  # 平均变化率阈值
  # 透明度检测方案选择
  scheme: "contrast_change"  # 可选: "adaptive_water", "fixed_dual", "pure_adaptive", "otsu_water", "multiscale_adaptive", "edge_enhanced", "contrast_change"
  
  # 对比度变化检测参数（仅当scheme为"contrast_change"时使用）
  contrast_change:
    threshold_factor: 1.5  # 统计差距的倍数因子
    change_rate_threshold: 0.3  # 变化率阈值（30%）
    absolute_diff_threshold: 15.0  # 绝对差距阈值
    min_consecutive_rows: 3  # 连续满足条件的最小行数
    search_window: 50  # 从下往上搜索的窗口大小（像素）
    water_region_height_factor: 0.5  # 水区域高度因子（相对于图像高度）
    water_region_min_height: 200  # 水区域最小高度（像素）

# 水尺检测参数
water_rule:
  blur_ksize: 5  # 高斯模糊核大小
  white_threshold: 180  # 白色阈值 (0-255)
  dilation_kernel_size: 3  # 膨胀操作核大小
  closing_kernel_size: 5  # 闭运算核大小
  sky_area_factor: 0.1  # 天空区域面积因子（相对于图像面积）
  roi_width_half: 100  # ROI宽度的一半（像素）
  length: 5 # 水尺完整长度为5m
  # 水尺顶部检测方法选择
  top_detection_method: "edge_detection"  # 可选: "e_letter", "edge_detection", "texture_similarity", "line_detection", "pattern_matching", "multi_method"
  # 水尺顶部检测搜索区域宽度（像素）
  top_detection_search_width: 200  # 基于中轴线向两侧扩展的总宽度

# 字母E形状识别参数
e_shape_detection:
  enable_two_pass: false  # 是否启用两轮匹配识别
  overlap_threshold: 0.5  # 虚拟轮廓与候选轮廓的重叠比例阈值
  min_area: 100  # 轮廓的最小面积阈值
  max_area: 10000  # 轮廓的最大面积阈值
  aspect_ratio_min: 0.5  # 宽高比下限
  aspect_ratio_max: 2.0  # 宽高比上限
  solidity_min: 0.4  # 坚实度下限
  solidity_max: 0.9  # 坚实度上限

# 多方法水尺顶部检测参数
ruler_top_detection:
  # 边缘检测方法参数
  edge_detection:
    canny_low: 50  # Canny边缘检测低阈值
    canny_high: 150  # Canny边缘检测高阈值
    min_line_width_factor: 0.3  # 最小线条宽度因子（相对于ROI宽度）
    
  # 纹理相似性分析参数
  texture_similarity:
    window_size: 50  # 滑动窗口大小
    step_size: 10  # 滑动步长
    similarity_threshold: 0.8  # 相似性阈值
    roi_width: 200  # 分析区域宽度
    
  # 霍夫直线检测参数
  line_detection:
    canny_low: 50  # Canny边缘检测低阈值
    canny_high: 150  # Canny边缘检测高阈值
    hough_threshold: 50  # 霍夫直线检测阈值
    min_line_length_factor: 0.3  # 最小线长因子（相对于ROI宽度）
    max_line_gap: 10  # 最大线段间隙
    max_angle: 15  # 水平线最大角度偏差（度）
    
  # 模式匹配参数
  pattern_matching:
    pattern_height: 30  # 模式高度
    roi_width: 150  # 分析区域宽度
    response_threshold_factor: 1.5  # 响应阈值因子（相对于平均响应）
    
  # 综合检测参数
  multi_method:
    consistency_threshold_high: 20  # 高一致性阈值（像素）
    consistency_threshold_medium: 50  # 中等一致性阈值（像素）
    confidence_factor: 0.3  # 信心度计算因子

# 主程序参数
main:
  grid_size: 100  # 网格大小
  default_method: "transparency"  # 默认检测方法: "gradient", "hough", "color_threshold", "transparency"
  default_roi: [532, 0, 200, 1706]  # 默认ROI: [x, y, width, height]，育王岭测试照片[1700, 1500, 200, 3000] 
  debug: false  # 是否启用调试模式
  brightness_threshold: 150  # 亮度阈值 - 对于透明度检测方法
  image_file: "water_level2.jpg"  # 默认图像文件名 