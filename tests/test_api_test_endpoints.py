"""
测试API接口的单元测试

验证测试接口的可用性和正确性。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from api.test import router as test_router
from services.test_service import TestService
from utils.config_manager import ConfigManager


@pytest.fixture
def mock_config_manager():
    """模拟配置管理器"""
    config_manager = Mock(spec=ConfigManager)
    config_manager.get_config.return_value = {
        "default_test_data": {
            "water_level": {
                "image_path": "data/test_cases/water_level/sample.jpg",
                "parameters": {
                    "method": "transparency",
                    "roi": None
                }
            }
        },
        "execution": {
            "timeout_seconds": 300,
            "max_concurrent_tests": 2
        }
    }
    return config_manager


@pytest.fixture
def mock_test_service(mock_config_manager):
    """模拟测试服务"""
    test_service = Mock(spec=TestService)
    
    # 模拟测试算法方法
    test_service.test_algorithm = AsyncMock(return_value={
        "algorithm": "water_level",
        "status": "completed",
        "test_cases": [
            {
                "case_name": "test_case_1",
                "status": "passed",
                "execution_time": 2.5,
                "result": {
                    "water_level": 1.25,
                    "confidence": 0.95,
                    "method": "transparency"
                },
                "error": None
            }
        ],
        "summary": {
            "total_cases": 1,
            "passed": 1,
            "failed": 0,
            "error": 0,
            "success_rate": 100.0,
            "avg_execution_time": 2.5
        },
        "timestamp": "2025-01-01T12:00:00",
        "debug_info": {
            "intermediate_results": "data/test_results/...",
            "logs": "data/logs/..."
        }
    })
    
    # 模拟测试所有算法方法
    test_service.test_all_algorithms = AsyncMock(return_value={
        "status": "completed",
        "algorithms": {
            "water_level": {"status": "completed", "success_rate": 100.0},
            "flow_speed": {"status": "completed", "success_rate": 90.0},
            "anomaly": {"status": "completed", "success_rate": 95.0}
        },
        "overall_summary": {
            "total_algorithms": 3,
            "total_cases": 9,
            "passed_cases": 8,
            "failed_cases": 1,
            "overall_success_rate": 88.9
        },
        "timestamp": "2025-01-01T12:00:00"
    })
    
    # 模拟其他方法
    test_service.list_available_algorithms = AsyncMock(return_value=[
        "water_level", "flow_speed", "anomaly"
    ])
    
    test_service.list_test_cases = AsyncMock(return_value=[
        "water_level_case_1", "water_level_case_2"
    ])
    
    test_service.get_default_test_parameters = AsyncMock(return_value={
        "image_path": "data/test_cases/water_level/sample.jpg",
        "parameters": {
            "method": "transparency",
            "roi": None
        }
    })
    
    return test_service


@pytest.fixture
def test_app(mock_test_service):
    """创建测试用的FastAPI应用"""
    app = FastAPI()
    app.include_router(test_router, prefix="/api")
    
    # 模拟应用状态
    app.state.test_service = mock_test_service
    app.state.config = Mock()
    
    return app


@pytest.fixture
def client(test_app):
    """创建测试客户端"""
    return TestClient(test_app)


class TestTestAPI:
    """测试API接口测试类"""
    
    def test_list_algorithms(self, client, mock_test_service):
        """测试获取算法列表接口"""
        response = client.get("/api/test/algorithms")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert "water_level" in data
        assert "flow_speed" in data
        assert "anomaly" in data
        
        # 验证服务方法被调用
        mock_test_service.list_available_algorithms.assert_called_once()
    
    def test_list_test_cases(self, client, mock_test_service):
        """测试获取测试用例列表接口"""
        response = client.get("/api/test/cases/water_level")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 0
        
        # 验证服务方法被调用
        mock_test_service.list_test_cases.assert_called_once_with("water_level")
    
    def test_test_algorithm_get(self, client, mock_test_service):
        """测试GET方法测试算法接口"""
        response = client.get("/api/test/water_level")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "algorithm" in data
        assert "status" in data
        assert "test_cases" in data
        assert "summary" in data
        assert "timestamp" in data
        
        assert data["algorithm"] == "water_level"
        assert data["status"] == "completed"
        assert isinstance(data["test_cases"], list)
        assert isinstance(data["summary"], dict)
        
        # 验证服务方法被调用
        mock_test_service.test_algorithm.assert_called_once_with(
            algorithm="water_level",
            case_names=None
        )
    
    def test_test_algorithm_get_with_cases(self, client, mock_test_service):
        """测试GET方法指定测试用例"""
        response = client.get("/api/test/water_level?case_names=case1,case2")
        
        assert response.status_code == 200
        
        # 验证服务方法被调用，并传递了正确的用例列表
        mock_test_service.test_algorithm.assert_called_once_with(
            algorithm="water_level",
            case_names=["case1", "case2"]
        )
    
    def test_test_algorithm_post(self, client, mock_test_service):
        """测试POST方法测试算法接口"""
        request_data = {
            "parameters": {
                "method": "gradient",
                "roi": {"x": 100, "y": 100, "width": 500, "height": 300}
            },
            "case_names": ["test_case_1"]
        }
        
        response = client.post("/api/test/water_level", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert data["algorithm"] == "water_level"
        assert data["status"] == "completed"
        
        # 验证服务方法被调用
        mock_test_service.test_algorithm.assert_called_once_with(
            algorithm="water_level",
            case_names=["test_case_1"],
            custom_parameters=request_data["parameters"]
        )
    
    def test_test_all_algorithms(self, client, mock_test_service):
        """测试所有算法测试接口"""
        response = client.get("/api/test/all")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "status" in data
        assert "algorithms" in data
        assert "overall_summary" in data
        assert "timestamp" in data
        
        assert data["status"] == "completed"
        assert isinstance(data["algorithms"], dict)
        assert isinstance(data["overall_summary"], dict)
        
        # 验证服务方法被调用
        mock_test_service.test_all_algorithms.assert_called_once()
    
    def test_get_default_parameters(self, client, mock_test_service):
        """测试获取默认参数接口"""
        response = client.get("/api/test/water_level/parameters")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "algorithm" in data
        assert "default_parameters" in data
        assert "timestamp" in data
        
        assert data["algorithm"] == "water_level"
        assert isinstance(data["default_parameters"], dict)
        
        # 验证服务方法被调用
        mock_test_service.get_default_test_parameters.assert_called_once_with("water_level")
    
    def test_invalid_algorithm(self, client, mock_test_service):
        """测试无效算法名称"""
        # 模拟服务抛出异常
        mock_test_service.test_algorithm.side_effect = ValueError("不支持的算法: invalid_algo")
        
        response = client.get("/api/test/invalid_algo")
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "不支持的算法" in data["detail"]
    
    def test_service_error(self, client, mock_test_service):
        """测试服务错误处理"""
        # 模拟服务抛出异常
        mock_test_service.test_algorithm.side_effect = Exception("测试服务错误")
        
        response = client.get("/api/test/water_level")
        
        assert response.status_code == 500
        data = response.json()
        assert "detail" in data
        assert "测试服务错误" in data["detail"]


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
