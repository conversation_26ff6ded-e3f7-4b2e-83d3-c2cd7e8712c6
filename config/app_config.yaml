# VR视频分析系统配置文件
# 本文件包含系统运行所需的所有配置参数

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  debug: false

# 并发控制配置
concurrency:
  max_concurrent_streams: 5        # 最大并发视频流数
  max_tasks_per_stream: 3         # 每个流最大并发任务数
  task_queue_size: 50             # 任务队列大小
  processor_pool_size: 6          # 处理器池大小

# 分析器配置
analyzers:
  # 水位识别配置
  water_level:
    enabled: true
    default_method: "transparency"  # 默认检测方法: transparency/gradient/hough/color_threshold/ruler_detection
    max_analysis_duration: 300     # 最大分析时长(秒)
    processor_instances: 2          # 处理器实例数
    
    # 各种检测方法的参数
    methods:
      transparency:
        alpha_thresh: 50
        canny_low: 30
        canny_high: 100
        blur_ksize: 5
        morph_kernel_size: 5
        min_area_factor: 0.01
        brightness_thresh: 80
        black_pixel_ratio_thresh: 0.5
        avg_change_rate_thresh: 0.4
        scheme: "contrast_change"   # 透明度检测方案
        # 对比度变化检测参数
        contrast_change:
          threshold_factor: 1.5
          change_rate_threshold: 0.3
          absolute_diff_threshold: 15.0
          min_consecutive_rows: 3
          search_window: 50
          water_region_height_factor: 0.5
          water_region_min_height: 200

      gradient:
        grad_thresh: 25
        blur_kernel_size: 5
        morph_kernel_size: 3

      hough:
        thresh: 70
        min_line_len_factor: 0.3
        max_gap: 20

      color_threshold:
        hsv_lower: [35, 40, 40]
        hsv_upper: [85, 255, 255]
        morph_kernel_size: 5
        min_area_factor: 0.01

      ruler_detection:
        white_threshold: 180
        dilation_kernel_size: 3
        closing_kernel_size: 5
        sky_area_factor: 0.1
        roi_width_half: 100
        length: 5  # 水尺完整长度为5m
        top_detection_method: "edge_detection"
        top_detection_search_width: 200

    # 图像预处理参数
    image_preprocessing:
      blur_kernel_size: 5
      morph_kernel_size: 3

    # 字母E形状识别参数
    e_shape_detection:
      enable_two_pass: false
      overlap_threshold: 0.5
      min_area: 100
      max_area: 10000
      aspect_ratio_min: 0.5
      aspect_ratio_max: 2.0
      solidity_min: 0.4
      solidity_max: 0.9

    # 水尺顶部检测参数
    ruler_top_detection:
      edge_detection:
        canny_low: 50
        canny_high: 150
        min_line_width_factor: 0.3
      texture_similarity:
        window_size: 50
        step_size: 10
        similarity_threshold: 0.8
        roi_width: 200
      line_detection:
        canny_low: 50
        canny_high: 150
        hough_threshold: 50
        min_line_length_factor: 0.3
        max_line_gap: 10
        max_angle: 15
      pattern_matching:
        pattern_height: 30
        roi_width: 150
        response_threshold_factor: 1.5
      multi_method:
        consistency_threshold_high: 20
        consistency_threshold_medium: 50
        confidence_factor: 0.3

  # 流速识别配置
  flow_speed:
    enabled: true
    default_method: "otv"           # 默认方法: otv/piv
    max_analysis_duration: 600     # 最大分析时长(秒)
    processor_instances: 2
    
    # OTV算法参数
    otv:
      feature_params:
        maxCorners: 100
        qualityLevel: 0.3
        minDistance: 7
        blockSize: 7
      lk_params:
        winSize: [15, 15]
        maxLevel: 2
        criteria_eps: 0.03
        criteria_count: 10
      tracking:
        max_track_len: 10
        track_interval: 5
        detect_interval: 5
    
    # PIV算法参数
    piv:
      window_size: 32
      overlap: 16
      search_area_size: 64
      dt: 0.02

  # 异常检测配置
  anomaly:
    enabled: true
    model_path: "models/yolo11n.pt"
    confidence_threshold: 0.5
    processor_instances: 2
    
    # 检测目标配置
    detection_targets:
      - "person"
      - "boat" 
      - "vehicle"
      - "bicycle"
      - "motorcycle"
    
    # YOLO模型参数
    yolo:
      input_size: 640
      nms_threshold: 0.45
      max_detections: 100

# 视频流处理配置
video_processing:
  supported_protocols: ["rtmp", "rtsp", "http", "https"]
  connection_timeout: 30          # 连接超时(秒)
  reconnect_attempts: 3           # 重连尝试次数
  frame_buffer_size: 100          # 帧缓冲区大小
  frame_extraction_interval: 1    # 帧提取间隔(秒)
  
  # 视频解码参数
  decode_params:
    backend: "ffmpeg"             # 解码后端
    thread_count: 4               # 解码线程数
    buffer_size: 1024             # 缓冲区大小(KB)

# 资源管理配置
resource_management:
  max_memory_usage: "4GB"         # 最大内存使用
  cleanup_interval: 300           # 资源清理间隔(秒)
  idle_connection_timeout: 600    # 空闲连接超时(秒)
  
  # 临时文件管理
  temp_files:
    max_size: "1GB"               # 临时文件最大总大小
    cleanup_age: 3600             # 临时文件清理时间(秒)
    base_path: "data/temp"        # 临时文件基础路径

# 日志配置
logging:
  level: "INFO"                   # 日志级别: DEBUG/INFO/WARNING/ERROR/CRITICAL
  file_path: "data/logs/app.log"  # 日志文件路径
  max_file_size: "10MB"           # 单个日志文件最大大小
  backup_count: 5                 # 日志文件备份数量
  
  # 日志格式配置
  format:
    console: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"

# 安全配置
security:
  api_key_required: false         # 是否需要API密钥
  api_key: ""                     # API密钥(如果启用)
  rate_limit:
    enabled: true                 # 是否启用速率限制
    requests_per_minute: 60       # 每分钟最大请求数
    burst_size: 10                # 突发请求大小
  
  # 输入验证
  validation:
    max_url_length: 2048          # 最大URL长度
    allowed_domains: []           # 允许的域名列表(空表示不限制)
    blocked_domains: []           # 禁止的域名列表

# 监控配置
monitoring:
  enabled: true                   # 是否启用监控
  metrics_interval: 60            # 指标收集间隔(秒)
  health_check_interval: 30       # 健康检查间隔(秒)
  
  # 性能指标
  performance:
    track_response_time: true     # 跟踪响应时间
    track_memory_usage: true      # 跟踪内存使用
    track_cpu_usage: true         # 跟踪CPU使用
    track_task_queue: true        # 跟踪任务队列状态
