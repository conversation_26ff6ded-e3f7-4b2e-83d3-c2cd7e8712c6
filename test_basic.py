#!/usr/bin/env python3
"""
基础功能测试脚本

测试VR视频分析系统的基础功能是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试基础模块导入"""
    print("测试基础模块导入...")
    
    try:
        from utils.config_manager import ConfigManager
        print("✓ ConfigManager 导入成功")
    except Exception as e:
        print(f"✗ ConfigManager 导入失败: {e}")
        return False
    
    try:
        from utils.logger import setup_logger
        print("✓ Logger 导入成功")
    except Exception as e:
        print(f"✗ Logger 导入失败: {e}")
        return False
    
    try:
        from utils.video_utils import VideoStreamProcessor
        print("✓ VideoStreamProcessor 导入成功")
    except Exception as e:
        print(f"✗ VideoStreamProcessor 导入失败: {e}")
        return False
    
    try:
        from utils.image_utils import ImageProcessor
        print("✓ ImageProcessor 导入成功")
    except Exception as e:
        print(f"✗ ImageProcessor 导入失败: {e}")
        return False
    
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from utils.config_manager import ConfigManager
        
        # 设置配置文件路径
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        
        config_manager = ConfigManager()
        
        # 测试基础配置获取
        server_config = config_manager.get("server")
        if server_config:
            print("✓ 服务器配置获取成功")
            print(f"  主机: {server_config.get('host')}")
            print(f"  端口: {server_config.get('port')}")
        else:
            print("✗ 服务器配置获取失败")
            return False
        
        # 测试嵌套配置获取
        water_level_enabled = config_manager.get("analyzers.water_level.enabled")
        print(f"✓ 水位分析器启用状态: {water_level_enabled}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def test_logger():
    """测试日志系统"""
    print("\n测试日志系统...")
    
    try:
        from utils.logger import setup_logger
        import logging
        
        # 初始化日志系统
        setup_logger(debug=True)
        
        # 创建测试日志器
        test_logger = logging.getLogger("test_logger")
        
        # 测试不同级别的日志
        test_logger.debug("这是一条调试信息")
        test_logger.info("这是一条信息")
        test_logger.warning("这是一条警告")
        test_logger.error("这是一条错误信息")
        
        print("✓ 日志系统测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        return False

def test_algorithms():
    """测试算法模块"""
    print("\n测试算法模块...")
    
    # 测试水位算法
    try:
        from algorithms.water_level.transparency import detect_waterline_transparency
        from algorithms.water_level.gradient import detect_waterline_gradient
        from algorithms.water_level.hough import detect_waterline_hough
        from algorithms.water_level.color_threshold import detect_waterline_color_threshold
        print("✓ 水位识别算法导入成功")
    except Exception as e:
        print(f"✗ 水位识别算法导入失败: {e}")
        return False
    
    # 测试流速算法
    try:
        from algorithms.flow_speed.otv import analyze_flow_otv
        print("✓ OTV算法导入成功")
    except Exception as e:
        print(f"✗ OTV算法导入失败: {e}")
        return False
    
    try:
        from algorithms.flow_speed.piv import analyze_flow_piv
        print("✓ PIV算法导入成功")
    except Exception as e:
        print(f"✗ PIV算法导入失败（可能缺少OpenPIV库）: {e}")
    
    # 测试异常检测算法
    try:
        from algorithms.anomaly.yolo_detector import detect_anomalies_yolo
        print("✓ YOLO检测器导入成功")
    except Exception as e:
        print(f"✗ YOLO检测器导入失败（可能缺少Ultralytics库）: {e}")
    
    return True

def test_processors():
    """测试处理器"""
    print("\n测试处理器...")
    
    try:
        from utils.config_manager import ConfigManager
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        config_manager = ConfigManager()
        
        # 测试水位处理器
        try:
            from processors.water_level_processor import WaterLevelProcessor
            processor = WaterLevelProcessor(config_manager)
            methods = processor.get_supported_methods()
            print(f"✓ 水位处理器创建成功，支持方法: {methods}")
        except Exception as e:
            print(f"✗ 水位处理器创建失败: {e}")
            return False
        
        # 测试流速处理器
        try:
            from processors.flow_speed_processor import FlowSpeedProcessor
            processor = FlowSpeedProcessor(config_manager)
            methods = processor.get_supported_methods()
            print(f"✓ 流速处理器创建成功，支持方法: {methods}")
        except Exception as e:
            print(f"✗ 流速处理器创建失败: {e}")
            return False
        
        # 测试异常检测处理器
        try:
            from processors.anomaly_processor import AnomalyProcessor
            processor = AnomalyProcessor(config_manager)
            targets = processor.get_supported_targets()
            print(f"✓ 异常检测处理器创建成功，支持目标: {len(targets)}个")
        except Exception as e:
            print(f"✗ 异常检测处理器创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 处理器测试失败: {e}")
        return False

def test_analysis_service():
    """测试分析服务"""
    print("\n测试分析服务...")
    
    try:
        from utils.config_manager import ConfigManager
        from services.analysis_service import AnalysisService
        
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        config_manager = ConfigManager()
        analysis_service = AnalysisService(config_manager)
        
        print("✓ 分析服务创建成功")
        print(f"  最大并发流数: {analysis_service.max_concurrent_streams}")
        print(f"  每流最大任务数: {analysis_service.max_tasks_per_stream}")
        
        return True
        
    except Exception as e:
        print(f"✗ 分析服务测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("VR视频分析系统基础功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_manager,
        test_logger,
        test_algorithms,
        test_processors,
        test_analysis_service
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
