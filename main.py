"""
VR视频分析系统主入口文件

基于FastAPI的视频分析服务，提供水位识别、流速识别和异常检测功能。
"""

import asyncio
import argparse
import logging
import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger
from src.api.health import router as health_router
from src.api.analyze import router as analyze_router
from src.api.test import router as test_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理

    启动时初始化配置、日志等系统组件
    关闭时清理资源
    """
    # 启动时初始化
    logger = logging.getLogger(__name__)
    logger.info("正在启动VR视频分析系统...")

    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        app.state.config = config_manager

        # 初始化分析服务
        from src.services.analysis_service import AnalysisService
        analysis_service = AnalysisService(config_manager)
        app.state.analysis_service = analysis_service

        logger.info("VR视频分析系统启动完成")

        yield

    except Exception as e:
        logger.error(f"系统启动失败: {str(e)}")
        raise
    finally:
        # 关闭时清理
        logger.info("正在关闭VR视频分析系统...")
        if hasattr(app.state, 'analysis_service'):
            await app.state.analysis_service.cleanup()
        logger.info("VR视频分析系统已关闭")


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例

    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    app = FastAPI(
        title="VR视频分析系统",
        description="提供水位识别、流速识别和异常检测的视频分析API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册路由
    app.include_router(health_router, prefix="/api", tags=["系统"])
    app.include_router(analyze_router, prefix="/api", tags=["分析"])
    app.include_router(test_router, prefix="/api", tags=["测试"])

    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger = logging.getLogger(__name__)
        logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "内部服务器错误",
                    "details": str(exc) if os.getenv("DEBUG") else "请联系系统管理员"
                }
            }
        )

    return app


def main():
    """
    主函数，解析命令行参数并启动服务或测试
    """
    parser = argparse.ArgumentParser(description="VR视频分析系统")

    # 服务器相关参数
    parser.add_argument("--config", default="config/app_config.yaml", help="配置文件路径")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")

    # 测试相关参数
    parser.add_argument("--test", action="store_true", help="启动测试模式")
    parser.add_argument("--algorithm", help="要测试的算法名称 (water_level, flow_speed, anomaly)")
    parser.add_argument("--case", action="append", help="要运行的测试用例名称（可多次指定）")
    parser.add_argument("--all-tests", action="store_true", help="运行所有算法的测试")
    parser.add_argument("--list-algorithms", action="store_true", help="列出支持的算法")
    parser.add_argument("--list-cases", help="列出指定算法的测试用例")
    parser.add_argument("--report-format", choices=["html", "yaml", "json"], default="html",
                       help="测试报告格式")
    parser.add_argument("--no-report", action="store_true", help="不生成测试报告")

    args = parser.parse_args()

    # 设置环境变量
    os.environ["CONFIG_FILE"] = args.config
    if args.debug:
        os.environ["DEBUG"] = "true"

    # 初始化日志系统
    setup_logger(debug=args.debug)

    # 如果是测试模式，运行测试
    if args.test or args.algorithm or args.all_tests or args.list_algorithms or args.list_cases:
        run_tests(args)
        return

    # 否则启动API服务器
    # 创建应用实例
    app = create_app()

    # 启动服务器
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="debug" if args.debug else "info"
    )


def run_tests(args):
    """
    运行测试模式

    Args:
        args: 命令行参数
    """
    from src.testing.test_runner import TestRunner
    from pathlib import Path

    try:
        # 初始化测试运行器
        workspace_root = Path(".").resolve()
        runner = TestRunner(workspace_root)

        # 处理命令
        if args.list_algorithms:
            algorithms = runner.list_available_algorithms()
            print("支持的算法:")
            for algo in algorithms:
                print(f"  - {algo}")
            return

        if args.list_cases:
            try:
                cases = runner.list_test_cases(args.list_cases)
                print(f"{args.list_cases} 算法的测试用例:")
                for case in cases:
                    print(f"  - {case}")
            except Exception as e:
                print(f"获取测试用例失败: {e}")
                sys.exit(1)
            return

        if args.all_tests:
            # 运行所有算法测试
            print("开始运行所有算法测试...")
            result = runner.run_all_tests(
                generate_report=not args.no_report,
                report_format=args.report_format
            )

            print(f"\n=== 测试完成 ===")
            print(f"总算法数: {result['total_algorithms']}")
            print(f"总测试用例: {result['total_cases']}")
            print(f"通过: {result['passed_cases']}")
            print(f"失败: {result['failed_cases']}")
            print(f"错误: {result['error_cases']}")
            print(f"总成功率: {result['success_rate']:.1f}%")

            if result.get('report_path'):
                print(f"测试报告: {result['report_path']}")

        elif args.algorithm:
            # 运行指定算法测试
            print(f"开始运行 {args.algorithm} 算法测试...")
            result = runner.run_algorithm_tests(
                algorithm=args.algorithm,
                case_names=args.case,
                generate_report=not args.no_report,
                report_format=args.report_format
            )

            print(f"\n=== {args.algorithm} 测试完成 ===")
            print(f"测试用例: {result['total_cases']}")
            print(f"通过: {result['passed_cases']}")
            print(f"失败: {result['failed_cases']}")
            print(f"错误: {result['error_cases']}")
            print(f"成功率: {result['success_rate']:.1f}%")
            print(f"平均执行时间: {result['avg_execution_time']:.2f} 秒")
            print(f"状态: {result['status']}")

            if result.get('report_path'):
                print(f"测试报告: {result['report_path']}")

            print(f"详细结果: {result['result_file']}")

        else:
            print("测试模式使用说明:")
            print("  python main.py --test --algorithm water_level")
            print("  python main.py --test --algorithm water_level --case water_level_case_1")
            print("  python main.py --test --all-tests")
            print("  python main.py --list-algorithms")
            print("  python main.py --list-cases water_level")

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"测试运行失败: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
